<?php
session_start();
require_once 'admin/lib/db_connection.php';

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <?php include 'inc/head.php'; ?>

</head>


<body>
    <div class="page-wrapper">

        <!-- Preloader -->
        <!-- <div id="preloader">
            <div class="loader">
                <div class="load-circle">
                    <div></div>
                    <div></div>
                </div>
            </div>
        </div> -->
        <!-- Preloader end-->


        <section class="header-style  rounded-bottom-custom">
            <div class="d-flex justify-content-between align-items-start">
                <!-- Left -->
                <div class="d-flex align-items-center gap-2">
                    <div class="user-icon-outline d-flex align-items-center justify-content-center">
                        <i class="fas fa-user pointer" onclick="window.location.href='profile.php'"></i>
                    </div>
                    <div class="text-white">
                        <div>37°C</div>
                        <div class="small">Overcast Clouds</div>
                    </div>
                </div>

                <!-- Right -->
                <div class="d-flex align-items-center gap-3">
                    <div class="coin-btn d-flex align-items-center justify-content-center">
                        <i class="fas fa-coins text-dark"></i>
                        <span class="coin-text ms-1 text-dark">0</span>
                    </div>
                    <i class="fas fa-bell text-white fs-5"></i>
                    <i class="fas fa-shopping-cart text-white fs-5 pointer" onclick="window.location.href='cart.php'"></i>
                </div>
            </div>

            <!-- Search Row -->
            <form id="indexSearchForm" class="search-bar d-flex align-items-center bg-white" autocomplete="off" style="width:100%;" action="products.php" method="get">
                <i class="fas fa-search text-muted me-2"></i>
                <input type="text" class="form-control border-0 shadow-none" id="indexSearchInput" name="keyword" placeholder="Search products here" />
                <i class="fas fa-microphone text-muted mx-3"></i>
                <div class="vr mx-2"></div>
                <i class="fas fa-image text-muted"></i>
            </form>
        </section>
        <!-- ------------------------------ -->
        <section class="categories-sub-top px-3 py-2">
            <div class="d-flex flex-nowrap overflow-auto gap-3 justify-content-center">
                <?php

                $categories = $db->dbFetchArray("SELECT id, name, image FROM tabl_category WHERE status='active' AND is_deleted=0 ORDER BY sort_order ASC, name ASC");
                foreach ($categories as $cat):
                    $img = $cat['image'] ? $cat['image'] : 'assets/images/no-image.png';
                ?>
                    <div class="cat-card text-center">
                        <div class="cat-img-container text-center" style="margin: auto;">
                            <img src="<?= htmlspecialchars($img) ?>" alt="<?= htmlspecialchars($cat['name']) ?>" class="img-fluid" />
                        </div>
                        <div class="cat-name"><?= htmlspecialchars($cat['name']) ?></div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>

        <!-- ------------------------------ -->

        <!-- Header -->
        <header class="header shadow header-fixed border-0 d-none">
            <div class="container">
                <div class="header-content">
                    <div class="left-content">
                        <a href="javascript:void(0);" class="menu-toggler">
                            <i class="icon feather icon-menu"></i>
                        </a>
                    </div>
                    <div class="mid-content header-logo">
                        <a href="index.php">
                            <!-- <img class="logo app-logo" src="assets/images/logos/light/logo1.svg" alt="logo"> -->
                            <!-- <img class="logo-dark" src="assets/images/svg/logo.svg" alt="logo">
							<img class="logo-light" src="assets/images/svg/logo-white.svg" alt="logo"> -->
                            Kissan Smart
                        </a>
                    </div>
                    <div class="right-content">
                        <a href="search.html" class="search-icon">
                            <i class="icon feather icon-search"></i>
                        </a>
                    </div>
                </div>
            </div>
        </header>
        <!-- Header -->

        <!-- Sidebar -->
        <div class="dark-overlay"></div>
        <div class="container d-none">
            <div class="sidebar" style="background-image: url('assets/images/background/bg3.png');">
                <a href="profile.php" class="author-box">
                    <div class="dz-media">
                        <img src="assets/images/user-profile.jpg" alt="author-image">
                    </div>
                    <div class="dz-info">
                        <h5 class="name">John Doe</h5>
                        <span><EMAIL></span>
                    </div>
                </a>
                <ul class="nav navbar-nav">
                    <li>
                        <a class="nav-link active" href="index.php">
                            <span class="dz-icon">
                                <i class="icon feather icon-home"></i>
                            </span>
                            <span>Home</span>
                        </a>
                    </li>
                    <li>
                        <a class="nav-link" href="product-list.html">
                            <span class="dz-icon">
                                <i class="icon feather icon-layers"></i>
                            </span>
                            <span>Products</span>
                        </a>
                    </li>
                    <!-- <li>
                    <a class="nav-link" href="components.html">
                        <span class="dz-icon">
						<i class="icon feather icon-grid"></i>
					</span>
                        <span>Components</span>
                    </a>
                </li> -->
                    <!-- <li>
                    <a class="nav-link" href="pages.html">
                        <span class="dz-icon">
						<i class="icon feather icon-book-open"></i>
					</span>
                        <span>Pages</span>
                    </a>
                </li> -->
                    <!-- <li>
                    <a class="nav-link" href="featured.html">
                        <span class="dz-icon">
						<i class="icon feather icon-list"></i>
					</span>
                        <span>Featured</span>
                    </a>
                </li> -->
                    <li>
                        <a class="nav-link" href="wishlist.php">
                            <span class="dz-icon">
                                <i class="icon feather icon-heart"></i>
                            </span>
                            <span>Wishlist</span>
                        </a>
                    </li>
                    <li>
                        <a class="nav-link" href="order.html">
                            <span class="dz-icon">
                                <i class="icon feather icon-repeat"></i>
                            </span>
                            <span>Orders</span>
                        </a>
                    </li>
                    <li>
                        <a class="nav-link" href="cart.php">
                            <span class="dz-icon">
                                <i class="icon feather icon-shopping-cart"></i>
                            </span>
                            <span>My Cart</span>
                        </a>
                    </li>
                    <li>
                        <a class="nav-link" href="profile.php">
                            <span class="dz-icon">
                                <i class="icon feather icon-user"></i>
                            </span>
                            <span>Profile</span>
                        </a>
                    </li>
                    <li>
                        <a class="nav-link" href="welcome.html">
                            <span class="dz-icon">
                                <i class="icon feather icon-log-out"></i>
                            </span>
                            <span>Logout</span>
                        </a>
                    </li>
                </ul>
                <div class="sidebar-bottom">
                    <ul class="app-setting">
                        <li class="nav-color pb-2">
                            <a href="javascript:void(0);" data-bs-toggle="offcanvas" data-bs-target="#offcanvasBottom" aria-controls="offcanvasBottom">
                                <span class="dz-icon">
                                    <svg class="color-plate" width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M10.725.787a9.325 9.325 0 0 1 	8.425 7.65 2.35 2.35 0 0 1-.625 2.275 1.662 1.662 0 0 1-1.863.188c-.462-.225-.812-.6-1.25-.788a4.238 4.238 0 0 0-4.5.725A3.751 3.751 0 0 0 10 14.825c.237.725.75 1.25 1.012 1.987a1.713 1.713 0 0 1-.762 2.063 3.312 3.312 0 0 1-2.5.125A9.262 9.262 0 0 1 3.125 3.837a9.2 9.2 0 0 1 7.6-3.05zM2.162 11.6A8.112 8.112 0 0 0 8 17.85c.487.125 1.25.3 1.6 0 .35-.3.15-.9-.125-1.25a4.674 4.674 0 0 1-.725-1.388A5 5 0 0 1 10 9.925a5.187 5.187 0 0 1 3.6-1.4 5.063 5.063 0 0 1 3.137 1.025.887.887 0 0 0 .95.225c.4-.213.338-.75.263-1.125a8.413 8.413 0 0 0-1.963-3.95 8.087 8.087 0 0 0-11.937 0 8.075 8.075 0 0 0-1.9 6.9h.012z" />
                                        <path d="M14.313 4.862a1.575 1.575 0 1 1 .024 3.15 1.575 1.575 0 0 1-.024-3.15zm0 2.2a.637.637 0 1 0 0-1.274.637.637 0 0 0 0 1.274zm-4.075-4.075a1.575 1.575 0 1 1 0 3.15 1.575 1.575 0 0 1 0-3.15zm0 2.2a.637.637 0 1 0 0-1.274.637.637 0 0 0 0 1.274zM6.25 7.925a1.575 1.575 0 1 1 .025-3.15 1.575 1.575 0 0 1-.025 3.15zm0-2.2A.637.637 0 1 0 6.25 7a.637.637 0 0 0 0-1.275zm.125 4.675a1.575 1.575 0 1 1-3.15 0 1.575 1.575 0 0 1 3.15 0zm-2.2 0a.638.638 0 1 0 1.275 0 .638.638 0 0 0-1.275 0zm2.075 2.387a1.575 1.575 0 1 1 0 3.151 1.575 1.575 0 0 1 0-3.15zm0 2.213a.638.638 0 1 0 0-1.276.638.638 0 0 0 0 1.276z" />
                                    </svg>
                                </span>
                                <span>Color Theme</span>
                                <div class="color-active ms-auto">
                                    <span>Active</span>
                                    <div class="current-color"></div>
                                </div>
                            </a>
                        </li>
                        <li>
                            <a class="mode" href="javascript:void(0);">
                                <span class="dz-icon">
                                    <i class="icon feather icon-moon"></i>
                                </span>
                                <span>Dark Mode</span>
                                <div class="custom-switch">
                                    <input type="checkbox" class="switch-input theme-btn" id="toggle-dark-menu">
                                    <label class="custom-switch-label" for="toggle-dark-menu"></label>
                                </div>
                            </a>
                        </li>
                    </ul>
                    <div class="app-info">
                        <h6 class="name">Kissan Smart</h6>
                        <span class="ver-info">App Version 1.0</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- Sidebar End -->
        <style>

        </style>


        <!-- Banner Section -->
        <div class="page-content space-top mt-2">
            <div class="container">
                <!-- Banner -->
                <div class="dz-banner">
                    <div class="swiper banner-swiper">
                        <div class="swiper-wrapper">
                            <?php

                            $banners = $db->dbFetchArray("SELECT title, image FROM tabl_banners WHERE is_deleted=0 ORDER BY id DESC");
                            foreach ($banners as $banner):
                                $img = $banner['image'] ? $banner['image'] : 'assets/images/banner/default.png';
                            ?>
                                <div class="swiper-slide">
                                    <div class="banner-bg" style="background-image: url('<?= htmlspecialchars($img) ?>');"></div>
                                    <!-- <?php if (!empty($banner['title'])): ?>
                                        <div class="banner-content">
                                            <span class="font-w500">&nbsp;</span>
                                            <h2 class="offer"><?= htmlspecialchars($banner['title']) ?></h2>
                                        </div>
                                    <?php endif; ?> -->
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="swiper-pagination style-2"></div>
                    </div>
                </div>


                <!-- Banner End -->
                <style>
                    .swiper-slide,
                    .banner-bg {
                        min-height: 200px;
                    }

                    /* Preferred Crops Section */
                    .preferred-crops-main {
                        padding: 20px 0;
                        background: #f8f9fa;
                        margin: 20px 0;
                        border-radius: 15px;
                    }

                    .preferred-crops-main .title {
                        color: #2d5a27;
                        font-weight: 700;
                        margin-bottom: 15px;
                        padding-left: 15px;
                    }

                    .preferred-crops-main .category-card {
                        background: #fff;
                        border-radius: 12px;
                        padding: 15px 10px;
                        margin-bottom: 15px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        transition: transform 0.3s ease;
                    }

                    .preferred-crops-main .category-card:hover {
                        transform: translateY(-5px);
                    }

                    .preferred-crops-main .img-container {
                        margin-bottom: 10px;
                    }

                    .preferred-crops-main .img-container img {
                        width: 60px;
                        height: 60px;
                        object-fit: cover;
                        border-radius: 10px;
                    }

                    .preferred-crops-main .cat-title {
                        font-size: 14px;
                        font-weight: 600;
                        color: #333;
                        margin: 0;
                    }

                    /* .category-premium-card {
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  text-align: center;
}

.category-premium-card h6 {
  margin-top: 10px;
  font-size: 14px;
}

.category-premium-card .icon-wrap {
  margin-bottom: 5px;
} */
                </style>
                <!-- Category One -->


                <!-- ------------------------------- -->
                <section class="categories-main">
                    <div class="container">
                        <div class="row">
                            <h5 class="title font-w800 mb-3">Categories</h5>
                            <?php

                            $categories = $db->dbFetchArray("SELECT id, name, image FROM tabl_category WHERE status='active' AND is_deleted=0 ORDER BY sort_order ASC, name ASC");
                            foreach ($categories as $cat):
                                $img = $cat['image'] ? $cat['image'] : 'assets/images/no-image.png';
                            ?>
                                <div class="col-4">
                                    <div class="category-card text-center">
                                        <div class="img-container">
                                            <img src="<?= htmlspecialchars($img) ?>" alt="<?= htmlspecialchars($cat['name']) ?>" />
                                        </div>
                                        <h6 class="cat-title"><?= htmlspecialchars($cat['name']) ?></h6>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </section>



                <!-- ------------------------------- -->
                <section class="preferred-crops-main">
                    <div class="container">
                        <div class="row">
                            <h5 class="title font-w800 mb-3">My Crops</h5>
                            <div id="preferredCropsContainer">
                                <!-- Preferred crops will be loaded here -->
                            </div>
                        </div>
                    </div>
                </section>



                <!-- ------------------------------- -->
                <section class="categories pt-4  d-none">
                    <div class="container position-relative">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="title font-w800">Explore Crop Categories</h5>
                            <div>
                                <button class="crop-nav-btn" id="prevCropBtn">
                                    <i class="fa-solid fa-chevron-left"></i>
                                </button>

                                <button class="crop-nav-btn" id="nextCropBtn">
                                    <i class="fa-solid fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                        <div class="swiper cropCategorySwiper">
                            <div class="swiper-wrapper">

                                <!-- Card 1 -->
                                <div class="swiper-slide">
                                    <div class="category-premium-card">
                                        <div class="icon-wrap">
                                            <img src="https://cdn-icons-png.flaticon.com/512/2909/2909766.png" alt="Field Crops" width="60" height="60">
                                        </div>
                                        <h6>Field Crops</h6>
                                    </div>
                                </div>

                                <!-- Card 2 -->
                                <div class="swiper-slide">
                                    <div class="category-premium-card">
                                        <div class="icon-wrap">
                                            <img src="https://cdn-icons-png.flaticon.com/512/4341/4341139.png" alt="Horticulture" width="60" height="60">
                                        </div>
                                        <h6>Horticulture</h6>
                                    </div>
                                </div>

                                <!-- Card 3 -->
                                <div class="swiper-slide">
                                    <div class="category-premium-card">
                                        <div class="icon-wrap">
                                            <img src="https://cdn-icons-png.flaticon.com/512/2965/2965567.png" alt="Pesticides" width="60" height="60">
                                        </div>
                                        <h6>Pesticides</h6>
                                    </div>
                                </div>

                                <!-- Card 4 -->
                                <div class="swiper-slide">
                                    <div class="category-premium-card">
                                        <div class="icon-wrap">
                                            <img src="https://cdn-icons-png.flaticon.com/512/2163/2163438.png" alt="Fungicides" width="60" height="60">
                                        </div>
                                        <h6>Fungicides</h6>
                                    </div>
                                </div>

                                <!-- Card 5 -->
                                <div class="swiper-slide">
                                    <div class="category-premium-card">
                                        <div class="icon-wrap">
                                            <img src="https://cdn-icons-png.flaticon.com/512/3228/3228629.png" alt="Herbicides" width="60" height="60">
                                        </div>
                                        <h6>Herbicides</h6>
                                    </div>
                                </div>

                                <!-- Card 6 -->
                                <div class="swiper-slide">
                                    <div class="category-premium-card">
                                        <div class="icon-wrap">
                                            <img src="https://cdn-icons-png.flaticon.com/512/3753/3753091.png" alt="Bio Pesticides" width="60" height="60">
                                        </div>
                                        <h6>Bio Pesticides</h6>
                                    </div>
                                </div>

                                <!-- Card 7 -->
                                <div class="swiper-slide">
                                    <div class="category-premium-card">
                                        <div class="icon-wrap">
                                            <img src="https://cdn-icons-png.flaticon.com/512/5356/5356185.png" alt="Bio Fertilizer" width="60" height="60">
                                        </div>
                                        <h6>Bio Fertilizer</h6>
                                    </div>
                                </div>

                                <!-- Card 8 -->
                                <div class="swiper-slide">
                                    <div class="category-premium-card">
                                        <div class="icon-wrap">
                                            <img src="https://cdn-icons-png.flaticon.com/512/2909/2909826.png" alt="Chemical Fertilizer" width="60" height="60">
                                        </div>
                                        <h6>Chemical Fertilizer</h6>
                                    </div>
                                </div>

                                <!-- Card 9 -->
                                <div class="swiper-slide">
                                    <div class="category-premium-card">
                                        <div class="icon-wrap">
                                            <img src="https://cdn-icons-png.flaticon.com/512/2724/2724572.png" alt="Seeds" width="60" height="60">
                                        </div>
                                        <h6>Seeds</h6>
                                    </div>
                                </div>

                                <!-- Card 10 -->
                                <div class="swiper-slide">
                                    <div class="category-premium-card">
                                        <div class="icon-wrap">
                                            <img src="https://cdn-icons-png.flaticon.com/512/2913/2913469.png" alt="Seedlings" width="60" height="60">
                                        </div>
                                        <h6>Seedlings</h6>
                                    </div>
                                </div>

                            </div>
                        </div>

                    </div>
                </section>
                <!-- category one end -->

                <!-- reco -->
                <!-- Recommended Products Section -->
                <section class="recommended-products pt-4">
                    <div class=" position-relative">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="title font-w800 mb-0">Recommended Products</h5>
                            <button class="btn view-all-btn p-0">View All</button>
                        </div>

                        <div class="products-scroll d-flex overflow-auto gap-3">
                            <!-- Card 1 -->
                            <div class="product-card">
                                <img src="https://www.plantgrowthhormones.com/uploads/202315034/hexazinone-75-wdg70dcbb2d-c1b7-46be-b5c6-1fcb285c007d.png" alt="Product" />
                                <h6 class="product-title">No Virus Bio</h6>
                                <p class="product-desc">Viricide</p>
                            </div>

                            <!-- Card 2 -->
                            <div class="product-card">
                                <img src="https://www.plantgrowthhormones.com/uploads/202315034/hexazinone-75-wdg70dcbb2d-c1b7-46be-b5c6-1fcb285c007d.png" alt="Product" />
                                <h6 class="product-title">Antracol</h6>
                                <p class="product-desc">Natural ingredients for</p>
                            </div>

                            <!-- Card 3 -->
                            <div class="product-card">
                                <img src="https://www.plantgrowthhormones.com/uploads/202315034/hexazinone-75-wdg70dcbb2d-c1b7-46be-b5c6-1fcb285c007d.png" alt="Product" />
                                <h6 class="product-title">Topper 77</h6>
                                <p class="product-desc">Herbicide</p>
                            </div>

                            <!-- Card 4 (Extra scrollable) -->
                            <div class="product-card">
                                <img src="https://cdn-icons-png.flaticon.com/512/4341/4341139.png" alt="Product" />
                                <h6 class="product-title">Antracol</h6>
                                <p class="product-desc">Natural ingredients for</p>
                            </div>
                        </div>
                    </div>
                </section>


                <!-- reco -->
                <!-- post -->
                <section class="post-issue-section mt-4">
                    <div class="post-input-wrapper d-flex align-items-center">
                        <i class="fa fa-edit input-icon me-2"></i>
                        <input type="text" class="form-control post-input" placeholder="Post issue" />
                        <button class="btn post-btn ms-2">Post</button>
                    </div>
                </section>

                <!-- post -->

                <!-- Catagory Start -->
                <!-- <div class="swiper category-swiper">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide">
                            <a href="javascript:void(0);" class="category-btn">All</a>
                        </div>
                        <div class="swiper-slide">
                            <a href="javascript:void(0);" class="category-btn">Men</a>
                        </div>
                        <div class="swiper-slide">
                            <a href="javascript:void(0);" class="category-btn">T-Shirt</a>
                        </div>
                        <div class="swiper-slide">
                            <a href="javascript:void(0);" class="category-btn">Women</a>
                        </div>
                        <div class="swiper-slide">
                            <a href="javascript:void(0);" class="category-btn">Winter</a>
                        </div>
                    </div>
                </div> -->
                <!-- Catagory End -->
                <div class="title-bar container mb-0 pb-0">
                    <h5 class="title font-w800 mb-0">Featured Products</h5>
                    <!-- <p class="text-muted small">Crop protection items (fungicides, pesticides, etc.)</p> -->
                </div>

                <div class="container pb-4">
                    <div class="row g-3">
                        <?php

                        $products = $db->dbFetchArray("SELECT p.id, p.title, p.product_type, p.status, p.product_code, p.category_id, c.name as category_name FROM tabl_products p LEFT JOIN tabl_category c ON p.category_id = c.id WHERE p.status='active' AND p.deleted_at IS NULL AND p.is_featured=1 ORDER BY p.id DESC LIMIT 12");
                        if ($products) {
                            foreach ($products as $product):
                                $imgRow = $db->dbFetchAssoc("SELECT image_path FROM tabl_product_images WHERE product_id = ? AND is_cover = 1 AND status = 'active' ORDER BY sort_order ASC, id ASC LIMIT 1", [$product['id']]);
                                $img = ($imgRow && $imgRow['image_path']) ? "./assets/uploads/products/" . $imgRow['image_path'] : 'assets/images/no-image.png';
                        ?>
                                <?php
                                // Get variations for this product
                                $variations = $db->dbFetchArray("SELECT id, price FROM tabl_product_variations WHERE product_id = ? AND status = 'active'", [$product['id']]);
                                $isSimple = ($product['product_type'] === 'simple' && count($variations) === 1);
                                $variationId = $isSimple ? $variations[0]['id'] : '';
                                ?>
                                <div class="col-6">
                                    <div class="shop-card">
                                        <div class="dz-media">
                                            <a href="product-detail.php?id=<?= $product['id'] ?>">
                                                <img src="<?= htmlspecialchars($img) ?>" alt="<?= htmlspecialchars($product['title']) ?>" />
                                            </a>
                                        </div>
                                        <div class="dz-content">
                                            <span class="font-12"><?= htmlspecialchars($product['category_name'] ?? $product['product_type']) ?></span>
                                            <h6 class="title"><a href="product-detail.php?id=<?= $product['id'] ?>"><?= htmlspecialchars($product['title']) ?></a></h6>
                                            <?php if ($isSimple): ?>
                                                <button class="btn btn-sm btn-success mt-1 add-featured-cart-btn" data-product-id="<?= $product['id'] ?>" data-variation-id="<?= $variationId ?>">Add to Cart</button>
                                            <?php else: ?>
                                                <button class="btn btn-sm btn-primary mt-1 view-detail-btn" data-product-id="<?= $product['id'] ?>">View Details</button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                        <?php endforeach;
                        } else {
                            echo '<div class="col-12 text-center text-muted">No featured products found.</div>';
                        }
                        ?>
                    </div>
                </div>
                <!-- -------------- -->
                <!-- ---------------- -->
                <!-- services -->
                <section class="services-home-section py-5">
                    <div class="container ">
                        <!-- <h2 class="mb-4">Our Services</h2> -->
                        <div class="ps-2">
                            <h5 class="title font-w800 mb-0">Our Services</h5>
                            <p class="text-muted small">
                                Empowering farmers with reliable services like soil testing, water analysis, crop monitoring, procurement, and more—tailored to modern agricultural needs.
                            </p>
                        </div>

                        <divj class="row justify-content-center text-center">
                            <!-- Service 1 -->
                            <div class="col-4 col-sm-3 col-md-2 mb-4">
                                <a href="services.html#soil-testing" class="text-decoration-none text-dark">
                                    <div class="service-icon-circle mb-2">
                                        <img src="data:image/jpeg;base64,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"
                                            class="img-fluid rounded-circle" alt="Soil Testing">
                                    </div>
                                    <p class="mb-0">Soil Testing</p>
                                </a>
                            </div>
                            <!-- Service 2 -->
                            <div class="col-4 col-sm-3 col-md-2 mb-4">
                                <a href="services.html#water-analysis" class="text-decoration-none text-dark">
                                    <div class="service-icon-circle mb-2">
                                        <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTQ_nReONEEWlbqNOLyIJ_yBPgyEv_RZOMk4w&s" class="img-fluid rounded-circle" alt="Water Analysis">
                                    </div>
                                    <p class="mb-0">Water Analysis</p>
                                </a>
                            </div>
                            <!-- Repeat same for other services -->
                            <div class="col-4 col-sm-3 col-md-2 mb-4">
                                <a href="services.html#crop-monitoring" class="text-decoration-none text-dark">
                                    <div class="service-icon-circle mb-2">
                                        <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQKYfr6QtmEqW9Tq3ZsTeBG1DUYoPu_9tXLyg&s" class="img-fluid rounded-circle" alt="Crop Monitoring">
                                    </div>
                                    <p class="mb-0">Crop Monitoring</p>
                                </a>
                            </div>
                            <div class="col-4 col-sm-3 col-md-2 mb-4">
                                <a href="services.html#procurement" class="text-decoration-none text-dark">
                                    <div class="service-icon-circle mb-2">
                                        <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS0HsrXYMKjbgP61HbNxaTSpp2gprY4QjVjBg&s" class="img-fluid rounded-circle" alt="Procurement">
                                    </div>
                                    <p class="mb-0">Procurement</p>
                                </a>
                            </div>
                            <div class="col-4 col-sm-3 col-md-2 mb-4">
                                <a href="services.html#exports" class="text-decoration-none text-dark">
                                    <div class="service-icon-circle mb-2">
                                        <img src="data:image/jpeg;base64,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"
                                            class="img-fluid rounded-circle" alt="Exports">
                                    </div>
                                    <p class="mb-0">Exports</p>
                                </a>
                            </div>
                            <div class="col-4 col-sm-3 col-md-2 mb-4">
                                <a href="services.html#kissan-connect" class="text-decoration-none text-dark">
                                    <div class="service-icon-circle mb-2">
                                        <img src="data:image/jpeg;base64,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"
                                            class="img-fluid rounded-circle" alt="Kissan Smart Connect">
                                    </div>
                                    <p class="mb-0">Kissan SmartConnect</p>
                                </a>
                            </div>
                    </div>
                    <!-- </div> -->
                </section>

                <!-- services -->

                <section class="section-green-feature p-3 pb-5 position-relative overflow-hidden">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-xl-7">
                                <div class="green-feature-content position-relative z-1">
                                    <div class="bg-floating-icon" style="background-image: url('https://bracketweb.com/alefox-html/assets/images/shapes/skill-1-shape.png');"></div>
                                    <h3 class="fw-bold mb-3 text-dark">Smart Crop Care for a Healthier Harvest</h3>
                                    <p class="text-muted mb-4">
                                        Empowering farmers with modern solutions to protect and nurture their crops. From organic pesticides to advanced protection tools — we bring innovation to your field.
                                    </p>
                                    <ul class="list-unstyled mb-4">
                                        <li class="mb-2"><i class="fas fa-leaf text-success me-2"></i> Trusted Organic Solutions</li>
                                        <li class="mb-2"><i class="fas fa-shield-alt text-success me-2"></i> Advanced Crop Protection</li>
                                        <li class="mb-2"><i class="fas fa-seedling text-success me-2"></i> Farmer-Friendly Products</li>
                                    </ul>
                                    <a href="contact.html" class="btn btn-success px-4 py-2 rounded-pill">
                                        Discover More
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- ------------------- -->
                <div class="title-bar container my-0">
                    <h5 class="title font-w800 mb-0">Trending Now</h5>
                    <a href="product.html">See more</a>
                </div>
                <!-- Shop Section Start -->
                <section class="container">
                    <div class="kissan-section position-relative">
                        <div class="swiper kissan-slider">
                            <div class="swiper-wrapper">
                                <?php
                                $trendingProducts = $db->dbFetchArray("SELECT p.id, p.title, p.product_type, p.status, p.product_code, p.category_id, c.name as category_name FROM tabl_products p LEFT JOIN tabl_category c ON p.category_id = c.id WHERE p.status='active' AND p.deleted_at IS NULL AND p.is_trending=1 ORDER BY p.id DESC LIMIT 12");
                                if ($trendingProducts) {
                                    foreach ($trendingProducts as $product):
                                        $imgRow = $db->dbFetchAssoc("SELECT image_path FROM tabl_product_images WHERE product_id = ? AND is_cover = 1 AND status = 'active' ORDER BY sort_order ASC, id ASC LIMIT 1", [$product['id']]);
                                        $img = ($imgRow && $imgRow['image_path']) ? "./assets/uploads/products/" . $imgRow['image_path'] : 'assets/images/no-image.png';
                                ?>
                                        <div class="swiper-slide">
                                            <div class="shop-card style-2">
                                                <div class="dz-media">
                                                    <a href="product-detail.php?id=<?= $product['id'] ?>"><img src="<?= htmlspecialchars($img) ?>" alt="<?= htmlspecialchars($product['title']) ?>"></a>
                                                    <!-- You can add product tags, reviews, etc. here if available -->
                                                </div>
                                                <div class="dz-content">
                                                    <span class="font-12"><?= htmlspecialchars($product['category_name'] ?? $product['product_type']) ?></span>
                                                    <h6 class="title"><a href="product-detail.php?id=<?= $product['id'] ?>"><?= htmlspecialchars($product['title']) ?></a></h6>
                                                </div>
                                            </div>
                                        </div>
                                <?php endforeach;
                                } else {
                                    echo '<div class="swiper-slide"><div class="shop-card style-2"><div class="dz-content text-center text-muted">No trending products found.</div></div></div>';
                                }
                                ?>
                            </div>

                            <!-- Arrows -->
                            <!-- <div class="swiper-button-next kissan-next"></div>
                        <div class="swiper-button-prev kissan-prev"></div> -->
                        </div>
                    </div>
                </section>
                <!-- Shop Section End -->


            </div>
        </div>
        <!-- Shop Section End -->

    </div>

    <div class="py-5"></div>

    <!-- Page Content End -->
    <!-- <nav class="app-bottom-navbar">
        <ul class="nav-items">
            <li class="nav-item active">
                <i class="fa-solid fa-house"></i>
                <span>Home</span>
            </li>
            <li class="nav-item">
                <i class="fa-solid fa-notes-medical"></i>
                <span>Crop Doctor</span>
            </li>
            <li class="nav-item">
                <i class="fa-solid fa-box"></i>
                <span>My Orders</span>
            </li>
            <li class="nav-item">
                <i class="fa-solid fa-people-group"></i>
                <span>Vedika</span>
            </li>
            <li class="nav-item">
                <i class="fa-solid fa-store"></i>
                <span>Agri Store</span>
            </li>
        </ul>
    </nav> -->




    <!-- Menubar -->
    <div class="menubar-area footer-fixed rounded-0">
        <div class="toolbar-inner menubar-nav">
            <a href="index.php" class="nav-link">
                <i class="icon feather icon-home"></i>
                <span>Home</span>
            </a>
            <a href="category.php" class="nav-link">
                <i class="icon feather icon-grid"></i>
                <span>Categories</span>
            </a>
            <a href="services.php" class="nav-link">
                <i class="icon feather icon-settings"></i>
                <span>Services</span>
            </a>
            <a href="cart.php" class="nav-link cart-handle">
                <div class="hexad-menu">
                    <img src="assets/images/menu-shape-dark.svg" class="shape-dark" alt="">
                    <img src="assets/images/menu-shape-light.svg" class="shape-light" alt="">
                    <i class="icon feather icon-shopping-bag"></i>
                </div>
            </a>
            <a href="profile.php" class="nav-link active">
                <i class="icon feather icon-user"></i>
                <span>Profile</span>
            </a>
        </div>
    </div>
    <!-- Menubar -->


    <!-- PWA Offcanvas -->
    <div class="offcanvas offcanvas-bottom pwa-offcanvas d-none">
        <div class="container">
            <div class="offcanvas-body small">
                <img class="logo dark" src="assets/images/logo.png" alt="">
                <img class="logo light" src="assets/images/logo-white.png" alt="">
                <h5 class="title">W3Cart - Ecommerce Mobile Shop on Your Home Screen</h5>
                <p class="pwa-text">Install "W3Cart Ecommerce Mobile App" template to your home screen for easy access, just like any other app</p>
                <button type="button" class="btn btn-sm btn-primary pwa-btn">Add to Home Screen</button>
                <button type="button" class="btn btn-sm pwa-close btn-secondary ms-2 text-white">Maybe later</button>
            </div>
        </div>
    </div>
    <div class="offcanvas-backdrop pwa-backdrop"></div>
    <!-- PWA Offcanvas End -->


    </div>
    <!--**********************************
    Scripts
***********************************-->

    <script src="assets/js/jquery.js"></script>
    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="assets/vendor/swiper/swiper-bundle.min.js"></script>
    <!-- Swiper -->
    <script src="assets/vendor/bootstrap-touchspin/dist/jquery.bootstrap-touchspin.min.js"></script>
    <!-- Swiper -->
    <script src="assets/js/dz.carousel.js"></script>
    <!-- Swiper -->
    <script src="assets/js/settings.js"></script>
    <script src="assets/js/custom.js"></script>
    <script src="index.js"></script>
    <script>
        $(document).on('click', '.add-featured-cart-btn', function(e) {
            e.preventDefault();
            var productId = $(this).data('product-id');
            var variationId = $(this).data('variation-id');
            var quantity = 1;
            $.ajax({
                url: 'ajax/addToCart.php',
                type: 'POST',
                data: {
                    product_id: productId,
                    variation_id: variationId,
                    quantity: quantity
                },
                dataType: 'json',
                success: function(res) {
                    if (res.success) {
                        if ($('#cart-count').length) {
                            var current = parseInt($('#cart-count').text()) || 0;
                            $('#cart-count').text(current + quantity);
                        }
                        alert('Added to cart!');
                    } else {
                        alert(res.message);
                    }
                },
                error: function() {
                    alert('Error adding to cart.');
                }
            });
        });
        $(document).on('click', '.view-detail-btn', function(e) {
            e.preventDefault();
            var productId = $(this).data('product-id');
            window.location.href = 'product-detail.php?id=' + productId;
        });
    </script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css">
    <script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>

    <script>
        new Swiper(".kissan-slider", {
            slidesPerView: 2,
            spaceBetween: 15,
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false
            },
            navigation: {
                nextEl: ".kissan-next",
                prevEl: ".kissan-prev",
            },
            breakpoints: {
                768: {
                    slidesPerView: 3,
                },
                1024: {
                    slidesPerView: 4,
                }
            }
        });
    </script>
    <script>
        const swiper = new Swiper('.category-swiper', {
            slidesPerView: 2,
            spaceBetween: 20,
            loop: false,
            // autoplay: {
            //     delay: 3000,
            //     disableOnInteraction: false
            // },
            navigation: {
                nextEl: '#nextBtn',
                prevEl: '#prevBtn',
            },
            slidesPerGroup: 1, // Slide 1 card at a time
            allowTouchMove: true, // Swipe on mobile
            breakpoints: {
                768: {
                    slidesPerView: 2,
                    slidesPerGroup: 1
                },
                1024: {
                    slidesPerView: 2,
                    slidesPerGroup: 1
                }
            }
        });
    </script>
    <script>
        const cropCategorySwiper = new Swiper('.cropCategorySwiper', {
            slidesPerView: 2,
            spaceBetween: 20,
            loop: false,
            slidesPerGroup: 1,
            navigation: {
                nextEl: '#nextCropBtn',
                prevEl: '#prevCropBtn',
            },
            breakpoints: {
                768: {
                    slidesPerView: 2
                },
                1024: {
                    slidesPerView: 2
                }
            }
        });

        // Load preferred crops
        function loadPreferredCrops() {
            $.ajax({
                url: 'ajax/get_preferred_crops.php',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.crops.length > 0) {
                        displayPreferredCrops(response.crops);
                    } else {
                        $('#preferredCropsContainer').html('<div class="col-12"><div class="alert alert-info">No preferred crops selected. <a href="login.php">Login</a> and <a href="preferred_crops.php">select your preferred crops</a> to see them here.</div></div>');
                    }
                },
                error: function() {
                    $('#preferredCropsContainer').html('<div class="col-12"><div class="alert alert-warning">Unable to load preferred crops.</div></div>');
                }
            });
        }

        function displayPreferredCrops(crops) {
            let html = '';
            crops.forEach(function(crop) {
                const cropImage = crop.images ? crop.images : '../assets/images/user.png';
                html += `
                    <div class="col-4">
                        <a href="products.php?crop_id=${crop.id}" class="text-decoration-none">
                            <div class="category-card text-center">
                                <div class="img-container">
                                    <img src="${cropImage}" alt="${crop.name}" />
                                </div>
                                <h6 class="cat-title">${crop.name}</h6>
                            </div>
                        </a>
                    </div>
                `;
            });
            $('#preferredCropsContainer').html('<div class="row">' + html + '</div>');
        }

        // Load preferred crops on page load
        $(document).ready(function() {
            loadPreferredCrops();
        });
    </script>

</body>


</html>