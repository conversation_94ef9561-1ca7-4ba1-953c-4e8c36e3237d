<?php
session_start();
require_once 'admin/lib/db_connection.php';

// Get service ID from URL if provided
$serviceId = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Get all active services
$services = $db->dbFetchArray("SELECT * FROM tabl_services WHERE status = 'active' AND is_deleted = 0 ORDER BY created_at ASC");

// If specific service ID is provided, redirect to service detail page
if ($serviceId > 0) {
    header('Location: services-single.php?id=' . $serviceId);
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <?php include 'inc/head.php'; ?>
    <title>Our Services - Kissan Smart</title>
</head>

<body>
    <div class="page-wrapper">
        <!-- Header -->
        <section class="header-style rounded-bottom-custom">
            <div class="d-flex justify-content-between align-items-start">
                <!-- Left -->
                <div class="d-flex align-items-center gap-2">
                    <div class="user-icon-outline d-flex align-items-center justify-content-center">
                        <i class="fas fa-arrow-left pointer" onclick="window.history.back()"></i>
                    </div>
                    <div class="text-white">
                        <h5 class="mb-0">Our Services</h5>
                        <div class="small">Empowering farmers with reliable services</div>
                    </div>
                </div>

                <!-- Right -->
                <div class="d-flex align-items-center gap-3">
                    <div class="coin-btn d-flex align-items-center justify-content-center">
                        <i class="fas fa-coins text-dark"></i>
                        <span class="coin-text ms-1 text-dark">0</span>
                    </div>
                    <i class="fas fa-bell text-white fs-5"></i>
                    <i class="fas fa-shopping-cart text-white fs-5 pointer" onclick="window.location.href='cart.php'"></i>
                </div>
            </div>
        </section>

        <!-- Services Content -->
        <div class="page-content space-top mt-2">
            <div class="container">
                <?php if (!empty($services)): ?>
                    <div class="row g-3">
                        <?php foreach ($services as $service):
                            // Get service icon and color based on name
                            $icon = 'fas fa-cogs';
                            $colorClass = 'service-default';
                            switch (strtolower($service['name'])) {
                                case 'soil testing':
                                    $icon = 'fas fa-flask';
                                    $colorClass = 'service-soil';
                                    break;
                                case 'water analysis':
                                    $icon = 'fas fa-tint';
                                    $colorClass = 'service-water';
                                    break;
                                case 'crop monitoring':
                                    $icon = 'fas fa-seedling';
                                    $colorClass = 'service-crop';
                                    break;
                                case 'procurement':
                                    $icon = 'fas fa-shopping-cart';
                                    $colorClass = 'service-procurement';
                                    break;
                                case 'exports':
                                    $icon = 'fas fa-plane';
                                    $colorClass = 'service-export';
                                    break;
                                case 'kissan smartconnect':
                                case 'kissan smart connect':
                                    $icon = 'fas fa-network-wired';
                                    $colorClass = 'service-connect';
                                    break;
                            }

                            $imageUrl = '';
                            if (!empty($service['image'])) {
                                $imageUrl = 'assets/uploads/services/' . $service['image'];
                            }
                        ?>
                            <div class="col-12 mb-3">
                                <div class="service-card <?= $colorClass ?>" onclick="window.location.href='services-single.php?id=<?= $service['id'] ?>'">
                                    <div class="service-card-content">
                                        <div class="service-icon">
                                            <?php if ($imageUrl && file_exists($imageUrl)): ?>
                                                <img src="<?= htmlspecialchars($imageUrl) ?>" alt="<?= htmlspecialchars($service['name']) ?>" class="service-image">
                                            <?php else: ?>
                                                <i class="<?= $icon ?>"></i>
                                            <?php endif; ?>
                                        </div>
                                        <div class="service-info">
                                            <h5 class="service-title"><?= htmlspecialchars($service['name']) ?></h5>
                                            <p class="service-description">
                                                <?= htmlspecialchars($service['short_description'] ?: substr(strip_tags($service['description']), 0, 100) . '...') ?>
                                            </p>
                                            <div class="service-arrow">
                                                <i class="fas fa-arrow-right"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                        <h4>No Services Available</h4>
                        <p class="text-muted">Services will be available soon.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <?php include 'inc/bottom-nav.php'; ?>
    </div>

    <style>
        .service-card {
            background: #fff;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border-left: 5px solid #28a745;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .service-card-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .service-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            font-size: 24px;
            flex-shrink: 0;
        }

        .service-image {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }

        .service-info {
            flex: 1;
        }

        .service-title {
            margin: 0 0 8px 0;
            color: #333;
            font-weight: 600;
        }

        .service-description {
            margin: 0;
            color: #666;
            font-size: 14px;
            line-height: 1.4;
        }

        .service-arrow {
            color: #28a745;
            font-size: 18px;
        }

        /* Service specific colors */
        .service-soil {
            border-left-color: #8B4513;
        }
        .service-soil .service-icon {
            background: linear-gradient(135deg, #8B4513, #A0522D);
        }

        .service-water {
            border-left-color: #007bff;
        }
        .service-water .service-icon {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .service-crop {
            border-left-color: #28a745;
        }
        .service-crop .service-icon {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .service-procurement {
            border-left-color: #ffc107;
        }
        .service-procurement .service-icon {
            background: linear-gradient(135deg, #ffc107, #e0a800);
        }

        .service-export {
            border-left-color: #6f42c1;
        }
        .service-export .service-icon {
            background: linear-gradient(135deg, #6f42c1, #5a32a3);
        }

        .service-connect {
            border-left-color: #fd7e14;
        }
        .service-connect .service-icon {
            background: linear-gradient(135deg, #fd7e14, #e8590c);
        }
    </style>

    <?php include 'inc/footer.php'; ?>
</body>
</html>